# AppSelect 组件新功能说明

## 概述

本次修改为 `AppSelect` 组件添加了完整的表单功能支持，使其与 `AppInput` 组件保持一致的设计风格和API接口。

## 新增功能

### 1. 必填字段支持

- **参数**: `required` (bool 类型，默认为 false)
- **功能**: 当设置为 true 时，在标签后显示红色星号(*)标识
- **使用示例**:
```dart
AppSelect<String>(
  label: '选择器标签',
  required: true,  // 显示红色星号
  options: options,
  // ...其他参数
)
```

### 2. 标签布局控制

#### 标签位置控制
- **参数**: `labelPosition` (LabelPosition 枚举，默认为 LabelPosition.top)
- **选项**: 
  - `LabelPosition.top`: 标签在顶部
  - `LabelPosition.left`: 标签在左侧
- **使用示例**:
```dart
AppSelect<String>(
  label: '选择器标签',
  labelPosition: LabelPosition.left,  // 标签在左侧
  options: options,
  // ...其他参数
)
```

#### 标签宽度控制
- **参数**: `labelWidth` (double? 类型，可选)
- **功能**: 仅在标签位置为左侧时生效，为null时自适应宽度
- **使用示例**:
```dart
AppSelect<String>(
  label: '选择器标签',
  labelPosition: LabelPosition.left,
  labelWidth: 120,  // 固定标签宽度为120
  options: options,
  // ...其他参数
)
```

### 3. 表单校验功能

#### 校验函数
- **参数**: `validator` (String? Function(T?)? 类型，可选)
- **功能**: 返回null表示验证通过，返回字符串表示错误信息
- **使用示例**:
```dart
AppSelect<String>(
  label: '选择器标签',
  options: options,
  validator: (value) {
    if (value == null) {
      return '请选择一个选项';
    }
    return null;
  },
  // ...其他参数
)
```

#### 自动校验模式
- **参数**: `autovalidateMode` (AutovalidateMode 枚举，默认为 AutovalidateMode.onUnfocus)
- **选项**:
  - `AutovalidateMode.disabled`: 禁用自动验证
  - `AutovalidateMode.always`: 始终自动验证
  - `AutovalidateMode.onUserInteraction`: 用户交互时验证
  - `AutovalidateMode.onUnfocus`: 失去焦点时验证（默认）

#### 错误信息显示
- **参数**: `showErrMsg` (bool 类型，默认为 true)
- **功能**: 控制是否显示校验错误信息
- **参数**: `errorText` (String? 类型，可选)
- **功能**: 自定义错误文本，通常不需要手动设置，会使用validator的返回值

## 完整使用示例

```dart
// 基本用法（顶部标签 + 必填 + 校验）
AppSelect<String>(
  label: '基本选择器',
  required: true,
  options: options,
  value: selectedValue,
  placeholder: '请选择一个选项',
  onChanged: (value) {
    setState(() {
      selectedValue = value;
    });
  },
  validator: (value) {
    if (value == null) {
      return '请选择一个选项';
    }
    return null;
  },
)

// 左侧标签 + 固定宽度
AppSelect<String>(
  label: '左侧标签选择器',
  labelPosition: LabelPosition.left,
  labelWidth: 100,
  required: true,
  options: options,
  value: selectedValue,
  clearable: true,
  onChanged: (value) {
    setState(() {
      selectedValue = value;
    });
  },
  validator: (value) {
    if (value == null) {
      return '此字段为必填项';
    }
    return null;
  },
)

// 多选模式 + 校验
AppSelect<String>(
  label: '多选选择器',
  required: true,
  multiple: true,
  options: options,
  values: multiSelectedValues,
  placeholder: '请选择多个选项',
  clearable: true,
  onMultiChanged: (values) {
    setState(() {
      multiSelectedValues = values;
    });
  },
  validator: (value) {
    if (multiSelectedValues.isEmpty) {
      return '至少选择一个选项';
    }
    return null;
  },
)
```

## 与 AppInput 的一致性

新增的功能与 `AppInput` 组件保持完全一致：

1. **参数命名**: 使用相同的参数名称和类型
2. **布局行为**: 标签位置和宽度控制逻辑相同
3. **校验机制**: 使用相同的 FormField 包装和校验逻辑
4. **错误显示**: 错误信息显示样式和动画效果一致
5. **必填标识**: 红色星号的显示方式相同

## 向后兼容性

所有新增参数都有合理的默认值，确保现有代码无需修改即可正常工作：

- `required`: 默认为 false
- `labelPosition`: 默认为 LabelPosition.top
- `labelWidth`: 默认为 null（自适应）
- `autovalidateMode`: 默认为 AutovalidateMode.onUnfocus
- `showErrMsg`: 默认为 true
- `validator`: 默认为 null
- `errorText`: 默认为 null

## 注意事项

1. 在多选模式下，校验器接收的是第一个选中项的值（如果有选中项），如果没有选中项则接收 null
2. 错误信息区域有固定高度，避免布局跳动
3. 标签宽度控制仅在 `labelPosition` 为 `LabelPosition.left` 时生效
4. 建议在使用校验功能时，将 AppSelect 包装在 Form 组件中以获得最佳体验
