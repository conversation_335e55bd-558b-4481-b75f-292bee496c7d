import 'package:flutter/material.dart';

class RawMessageModel with ChangeNotifier {
  //前段自己维护的Id 用于做ACk确认
  String? clientMessageId;
  // 设备类型 固定1
  int? deviceTypeenum;
  //消息体Dto枚举  1.主体消息 2.已阅消息 3.撤回消息
  int? imTypeenum;
  //消息Id
  String? messageIdStr;
  //发送方 肯定是员工Id
  String? fromEmployeeId;
  //发送方
  dynamic fromEmployee;
  //接受方 可能是员工id 或者群组id 通过IsGroup判断
  String? toReceiveId;
  //群组聊天就是true
  bool? isGroup;
  //消息体类型枚举 1.文本 2.图片 3.音频 ....
  int? imMessageTypeenum;
  //1.单聊 2.群聊 3.系统聊
  int? chatPartnerTypeenum;
  //具体消息内容
  String? content;
  //发送时间
  String? sendTime;
  //是否已读
  bool? isRead;
  //固定
  String? clientVersion;
  //引用的消息Id
  String? quoteMessageIdStr;
  //已读列表
  List<dynamic>? readMessageList;

  //是否选中
  bool _isSelected = false;

  bool get isSelected => _isSelected;

  set isSelected(bool value) {
    _isSelected = value;
    notifyListeners();
  }

  RawMessageModel({
    this.clientMessageId,
    this.deviceTypeenum,
    this.imTypeenum,
    this.messageIdStr,
    this.fromEmployeeId,
    this.fromEmployee,
    this.toReceiveId,
    this.isGroup,
    this.imMessageTypeenum,
    this.chatPartnerTypeenum,
    this.content,
    this.sendTime,
    this.isRead,
    this.clientVersion,
    this.quoteMessageIdStr,
    this.readMessageList,
  });

  factory RawMessageModel.fromJson(Map<String, dynamic> json) {
    return RawMessageModel(
      clientMessageId: json['ClientMessageId'] as String?,
      deviceTypeenum: json['DeviceTypeenum'] as int?,
      imTypeenum: json['IMTypeenum'] as int?,
      messageIdStr: json['MessageIdStr'] as String?,
      fromEmployeeId: json['FromEmployeeId'] as String?,
      fromEmployee: json['FromEmployee'],
      toReceiveId: json['ToReceiveId'] as String?,
      isGroup: json['IsGroup'] as bool?,
      imMessageTypeenum: json['IMMessageTypeenum'] as int?,
      chatPartnerTypeenum: json['ChatPartnerTypeenum'] as int?,
      content: json['Content'] as String?,
      sendTime: json['SendTime'] as String?,
      isRead: json['IsRead'] as bool?,
      clientVersion: json['ClientVersion'] as String?,
      quoteMessageIdStr: json['QuoteMessageIdStr'] as String?,
      readMessageList: json['ReadMessageList'] as List<dynamic>?,
    );
  }

  Map<String, dynamic> toJson() => {
    'ClientMessageId': clientMessageId,
    'DeviceTypeenum': deviceTypeenum,
    'IMTypeenum': imTypeenum,
    'MessageIdStr': messageIdStr,
    'FromEmployeeId': fromEmployeeId,
    'FromEmployee': fromEmployee,
    'ToReceiveId': toReceiveId,
    'IsGroup': isGroup,
    'IMMessageTypeenum': imMessageTypeenum,
    'ChatPartnerTypeenum': chatPartnerTypeenum,
    'Content': content,
    'SendTime': sendTime,
    'IsRead': isRead,
    'ClientVersion': clientVersion,
    'QuoteMessageIdStr': quoteMessageIdStr,
    'ReadMessageList': readMessageList,
  };
}
